#include <Wire.h>
#include <SparkFun_KX13X.h>
#include <Arduino.h>
SparkFun_KX132 kxAccel;
outputData myData;
rawOutputData rawData;

const int INT_PIN = 4;
const int IO_SEL_PIN = 18;

void printConfiguration() {
  Serial.println("=== Cấu hình KX132 ===");
  Serial.print("Sample Rate: "); Serial.print(kxAccel.getOutputDataRate()); Serial.println(" Hz");
  Serial.println("ADC Resolution: 16-bit");
  Serial.println("Buffer Resolution: 16-bit");
  Serial.println("Range: ±16G");
  Serial.print("Độ phân giải: "); Serial.println("0.488 mg/LSB");
  Serial.println("Buffer max samples: 86 (16-bit)");
  Serial.println("Độ chính xác hiển thị: 4 chữ số sau dấu phẩy");
  Serial.println("=====================");
}

void readMultipleBufferSamples() {
  uint16_t bufferLevel = kxAccel.getSampleLevel();
  if (bufferLevel >= 5) {
    Serial.println("\n=== Đọc 5 samples liên tiếp từ Buffer 16-bit ===");

    const double convRange16G = 0.000488; // mg/LSB cho range ±16G

    for (int i = 0; i < 5 && kxAccel.getSampleLevel() > 0; i++) {
      if (kxAccel.getRawAccelBufferData(&rawData, 1)) { // 1 = 16-bit
        // Chuyển đổi sang g với 4 chữ số sau dấu phẩy
        float xG = (float)rawData.xData * convRange16G;
        float yG = (float)rawData.yData * convRange16G;
        float zG = (float)rawData.zData * convRange16G;

        Serial.print("Sample #"); Serial.print(i+1); Serial.print(": ");
        Serial.print("X="); Serial.print(xG, 4);
        Serial.print("g, Y="); Serial.print(yG, 4);
        Serial.print("g, Z="); Serial.print(zG, 4); Serial.println("g");
      }
    }

    Serial.print("Buffer còn lại: ");
    Serial.print(kxAccel.getSampleLevel()); Serial.println(" samples");
    Serial.println("===============================================\n");
  }
}

void setup() {
  pinMode(INT_PIN, INPUT);
  pinMode(IO_SEL_PIN, OUTPUT);
  digitalWrite(IO_SEL_PIN, LOW); // Chọn địa chỉ I2C 0x1E

  Wire.begin(17, 16);  
  Serial.begin(115200);
  while (!Serial);  

  Serial.println("Initializing KX132...");

  if (!kxAccel.begin(0x1E)) {  // Địa chỉ 0x1E khi IO_SEL = LOW
    Serial.println("Không tìm thấy cảm biến KX132. Dừng.");
    while (1);
  }

  Serial.println("KX132 sẵn sàng.");

  if (kxAccel.softwareReset()) {
    Serial.println("Đã reset KX132.");
  }
  delay(10);

  kxAccel.enableAccel(false);
  kxAccel.enableDataEngine();
  kxAccel.enablePhysInterrupt();
  kxAccel.routeHardwareInterrupt(0x10);

  kxAccel.setRange(SFE_KX132_RANGE16G);

  // Cấu hình Buffer với 16-bit resolution
  kxAccel.setBufferResolution(true);  // true = 16-bit, false = 8-bit
  kxAccel.setBufferThreshold(30);     // 16-bit max 86 samples, dùng 30 để demo
  kxAccel.setBufferOperationMode(1);  // 1 = FIFO mode
  kxAccel.enableSampleBuffer(true);   // Bật buffer

  // Thiết lập sample rate tối đa (tùy chọn)
  kxAccel.setOutputDataRate(15);      // 15 = 25.6 kHz (max sample rate)

  kxAccel.enableAccel();

  // Hiển thị cấu hình
  delay(100);
  printConfiguration();
}

static unsigned long lastMultiRead = 0;

void loop() {
  if (digitalRead(INT_PIN) == HIGH) {
    // Đọc dữ liệu thông thường (16-bit ADC) với 4 chữ số sau dấu phẩy
    kxAccel.getAccelData(&myData);
    Serial.print("ADC 16-bit (g) - X: "); Serial.print(myData.xData, 4);
    Serial.print(" Y: "); Serial.print(myData.yData, 4);
    Serial.print(" Z: "); Serial.println(myData.zData, 4);

    // Kiểm tra buffer level
    uint16_t bufferLevel = kxAccel.getSampleLevel();
    Serial.print("Buffer hiện có "); Serial.print(bufferLevel); Serial.println(" samples");

    if (bufferLevel > 0) {
      // Đọc 1 sample từ buffer (16-bit)
      if (kxAccel.getRawAccelBufferData(&rawData, 1)) { // 1 = 16-bit
        // Chuyển đổi raw 16-bit sang g với độ chính xác 4 chữ số
        const double convRange16G = 0.000488; // mg/LSB cho range ±16G
        float xG = (float)rawData.xData * convRange16G;
        float yG = (float)rawData.yData * convRange16G;
        float zG = (float)rawData.zData * convRange16G;

        Serial.print("Buffer 16-bit (g) - X: "); Serial.print(xG, 4);
        Serial.print(" Y: "); Serial.print(yG, 4);
        Serial.print(" Z: "); Serial.println(zG, 4);
      }
    }

    Serial.println("---");
  }

  // Đọc nhiều samples mỗi 3 giây
  if (millis() - lastMultiRead > 3000) {
    readMultipleBufferSamples();
    lastMultiRead = millis();
  }

  delay(150);  // Delay để buffer tích lũy samples
}
