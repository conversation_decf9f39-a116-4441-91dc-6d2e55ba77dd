#include <Wire.h>
#include <SparkFun_KX13X.h>
#include <Arduino.h>
SparkFun_KX132 kxAccel;
outputData myData;
rawOutputData rawData;

const int INT_PIN = 4;
const int IO_SEL_PIN = 18;

void printConfiguration() {
  Serial.println("=== Cấu hình KX132 ===");
  Serial.print("Sample Rate: "); Serial.print(kxAccel.getOutputDataRate()); Serial.println(" Hz");
  Serial.println("Resolution: 16-bit");
  Serial.println("Range: ±16G");
  Serial.print("Độ phân giải: "); Serial.println("0.488 mg/LSB");
  Serial.println("Buffer: Enabled (16-bit)");
  Serial.println("=====================");
}

void setup() {
  pinMode(INT_PIN, INPUT);
  pinMode(IO_SEL_PIN, OUTPUT);
  digitalWrite(IO_SEL_PIN, LOW); // Chọn địa chỉ I2C 0x1E

  Wire.begin(17, 16);  
  Serial.begin(115200);
  while (!Serial);  

  Serial.println("Initializing KX132...");

  if (!kxAccel.begin(0x1E)) {  // Địa chỉ 0x1E khi IO_SEL = LOW
    Serial.println("Không tìm thấy cảm biến KX132. Dừng.");
    while (1);
  }

  Serial.println("KX132 sẵn sàng.");

  if (kxAccel.softwareReset()) {
    Serial.println("Đã reset KX132.");
  }
  delay(10);

  kxAccel.enableAccel(false);
  kxAccel.enableDataEngine();
  kxAccel.enablePhysInterrupt();
  kxAccel.routeHardwareInterrupt(0x10);

  kxAccel.setRange(SFE_KX132_RANGE16G);

  // Cấu hình Buffer với 16-bit resolution
  kxAccel.setBufferResolution(true);  // true = 16-bit, false = 8-bit
  kxAccel.setBufferThreshold(10);     // Số lượng samples trong buffer
  kxAccel.setBufferOperationMode(1);  // 1 = FIFO mode
  kxAccel.enableSampleBuffer(true);   // Bật buffer

  // Thiết lập sample rate tối đa (tùy chọn)
  kxAccel.setOutputDataRate(15);      // 15 = 25.6 kHz (max sample rate)

  kxAccel.enableAccel();

  // Hiển thị cấu hình
  delay(100);
  printConfiguration();
}

void loop() {
  if (digitalRead(INT_PIN) == HIGH) {
    // Đọc dữ liệu thông thường (16-bit)
    kxAccel.getAccelData(&myData);
    Serial.print("Normal 16-bit - X: "); Serial.print(myData.xData, 4);
    Serial.print(" Y: "); Serial.print(myData.yData, 4);
    Serial.print(" Z: "); Serial.println(myData.zData, 4);

    // Đọc dữ liệu raw 16-bit
    if (kxAccel.getRawAccelData(&rawData)) {
      Serial.print("Raw 16-bit - X: "); Serial.print(rawData.xData);
      Serial.print(" Y: "); Serial.print(rawData.yData);
      Serial.print(" Z: "); Serial.println(rawData.zData);
    }

    // Kiểm tra buffer level
    uint16_t bufferLevel = kxAccel.getSampleLevel();
    if (bufferLevel > 0) {
      Serial.print("Buffer có "); Serial.print(bufferLevel); Serial.println(" samples");

      // Đọc dữ liệu từ buffer (16-bit)
      if (kxAccel.getRawAccelBufferData(&rawData, 1)) { // 1 = 16-bit
        Serial.print("Buffer 16-bit - X: "); Serial.print(rawData.xData);
        Serial.print(" Y: "); Serial.print(rawData.yData);
        Serial.print(" Z: "); Serial.println(rawData.zData);
      }
    }

    Serial.println("---");
  }

  delay(100);  // Delay để dễ đọc output
}
