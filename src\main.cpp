#include <Wire.h>
#include <SparkFun_KX13X.h>
#include <Arduino.h>
SparkFun_KX132 kxAccel;
outputData myData;
rawOutputData rawData;

const int INT_PIN = 4;
const int IO_SEL_PIN = 18;

void printConfiguration() {
  Serial.println("KX132 Ready - 16bit Buffer, ±2G, 25.6kHz, 0.061mg/LSB");
}

// Xóa hàm này để giảm output không cần thiết

void setup() {
  pinMode(INT_PIN, INPUT);
  pinMode(IO_SEL_PIN, OUTPUT);
  digitalWrite(IO_SEL_PIN, LOW); // Chọn địa chỉ I2C 0x1E

  Wire.begin(17, 16);  
  Serial.begin(115200);
  while (!Serial);  

  if (!kxAccel.begin(0x1E)) {
    Serial.println("KX132 Error!");
    while (1);
  }

  kxAccel.softwareReset();
  delay(10);

  kxAccel.enableAccel(false);
  kxAccel.enableDataEngine();
  kxAccel.enablePhysInterrupt();
  kxAccel.routeHardwareInterrupt(0x10);

  kxAccel.setRange(SFE_KX132_RANGE2G);  // Chuyển sang chế độ ±2G

  // Cấu hình Buffer với 16-bit resolution
  kxAccel.setBufferResolution(true);  // true = 16-bit, false = 8-bit
  kxAccel.setBufferThreshold(30);     // 16-bit max 86 samples, dùng 30 để demo
  kxAccel.setBufferOperationMode(1);  // 1 = FIFO mode
  kxAccel.enableSampleBuffer(true);   // Bật buffer

  // Thiết lập sample rate tối đa (tùy chọn)
  kxAccel.setOutputDataRate(15);      // 15 = 25.6 kHz (max sample rate)

  kxAccel.enableAccel();

  // Hiển thị cấu hình
  delay(100);
  printConfiguration();
}

void loop() {
  if (digitalRead(INT_PIN) == HIGH) {
    // Đọc dữ liệu từ buffer 16-bit với interrupt
    if (kxAccel.getRawAccelBufferData(&rawData, 1)) { // 1 = 16-bit
      const double convRange2G = 0.000061; // mg/LSB cho range ±2G (độ chính xác cao nhất)
      float xG = (float)rawData.xData * convRange2G;
      float yG = (float)rawData.yData * convRange2G;
      float zG = (float)rawData.zData * convRange2G;

      Serial.print("X: "); Serial.print(xG, 4);
      Serial.print(" Y: "); Serial.print(yG, 4);
      Serial.print(" Z: "); Serial.println(zG, 4);
    }
  }

  delay(10); // Ngắn để phản hồi interrupt nhanh
}
